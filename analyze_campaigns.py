"""
分析广告活动状态脚本
"""
from wb_api_client import WildberriesAPIClient, MultiAccountAPIManager
from config import settings
from loguru import logger
import sys

def analyze_campaign_status():
    """分析广告活动状态"""

    # 指定要分析的广告ID列表
    target_campaign_ids = [
        ********, ********, ********, ********, ********,
        ********, ********, ********, ********, ********,
        ********, ********
    ]

    logger.info("开始获取所有广告活动...")

    try:
        # 使用多账号管理器获取所有广告活动
        manager = MultiAccountAPIManager()
        all_campaigns = manager.get_all_campaigns()

        logger.info(f"总共获取到 {len(all_campaigns)} 个广告活动")

        # 查找********的状态信息
        target_campaign = None
        for campaign in all_campaigns:
            if campaign.campaign_id == ********:
                target_campaign = campaign
                break

        if target_campaign:
            logger.info(f"找到目标广告 ********: 类型={target_campaign.type}, 状态={target_campaign.status}")

            # 查询所有相同类型和状态的广告
            same_status_campaigns = []
            for campaign in all_campaigns:
                if campaign.type == target_campaign.type and campaign.status == target_campaign.status:
                    same_status_campaigns.append(campaign)

            logger.info(f"找到 {len(same_status_campaigns)} 个相同状态的广告")
        else:
            logger.error("未找到目标广告 ********")
            return

        # 分析指定ID的广告状态
        found_campaigns = []
        not_found_ids = target_campaign_ids.copy()

        for campaign in all_campaigns:
            if campaign.campaign_id in target_campaign_ids:
                found_campaigns.append(campaign)
                if campaign.campaign_id in not_found_ids:
                    not_found_ids.remove(campaign.campaign_id)
        
        # 输出分析结果
        print("\n" + "="*80)
        print("广告活动状态分析结果")
        print("="*80)

        # 首先显示与********相同状态的所有广告
        if target_campaign and same_status_campaigns:
            print(f"\n与广告********相同状态的所有广告 (类型:{get_type_description(target_campaign.type)}, 状态:{get_status_description(target_campaign.status)}):")
            print(f"共找到 {len(same_status_campaigns)} 个广告")
            print("-" * 80)
            print(f"{'ID':<12} {'名称':<30} {'类型':<6} {'状态':<6} {'创建时间':<20} {'修改时间':<20}")
            print("-" * 80)

            for campaign in same_status_campaigns:
                status_desc = get_status_description(campaign.status)
                type_desc = get_type_description(campaign.type)

                # 高亮显示目标广告
                marker = " ★" if campaign.campaign_id == ******** else "  "
                print(f"{campaign.campaign_id:<12} {campaign.name[:28]:<30} {type_desc:<6} {status_desc:<6} "
                      f"{campaign.create_time.strftime('%Y-%m-%d %H:%M'):<20} "
                      f"{campaign.change_time.strftime('%Y-%m-%d %H:%M'):<20}{marker}")

        print(f"\n" + "="*80)
        print("指定ID列表的广告状态")
        print("="*80)

        if found_campaigns:
            print(f"\n找到 {len(found_campaigns)} 个指定的广告活动:")
            print("-" * 80)
            print(f"{'ID':<12} {'名称':<30} {'类型':<6} {'状态':<6} {'创建时间':<20} {'修改时间':<20}")
            print("-" * 80)

            for campaign in found_campaigns:
                status_desc = get_status_description(campaign.status)
                type_desc = get_type_description(campaign.type)

                print(f"{campaign.campaign_id:<12} {campaign.name[:28]:<30} {type_desc:<6} {status_desc:<6} "
                      f"{campaign.create_time.strftime('%Y-%m-%d %H:%M'):<20} "
                      f"{campaign.change_time.strftime('%Y-%m-%d %H:%M'):<20}")

        if not_found_ids:
            print(f"\n未找到以下 {len(not_found_ids)} 个广告活动ID:")
            print("-" * 40)
            for campaign_id in not_found_ids:
                print(f"ID: {campaign_id}")

        # 统计状态分布
        print(f"\n指定广告活动的状态统计:")
        print("-" * 40)
        status_count = {}
        for campaign in found_campaigns:
            status_desc = get_status_description(campaign.status)
            status_count[status_desc] = status_count.get(status_desc, 0) + 1

        for status, count in status_count.items():
            print(f"{status}: {count} 个")

        print("\n" + "="*80)
        
    except Exception as e:
        logger.error(f"分析广告活动状态失败: {e}")
        sys.exit(1)

def get_status_description(status: int) -> str:
    """获取状态描述"""
    status_map = {
        4: "暂停",      # 暂停
        7: "完成",      # 完成
        8: "归档",      # 归档
        9: "活跃",      # 活跃
        11: "停止",     # 停止
    }
    return status_map.get(status, f"未知({status})")

def get_type_description(type_code: int) -> str:
    """获取类型描述"""
    type_map = {
        4: "自动",      # 自动推广
        5: "搜索",      # 搜索推广
        6: "推荐",      # 推荐推广
        8: "品牌",      # 品牌推广
        9: "拍卖",      # 拍卖推广
    }
    return type_map.get(type_code, f"未知({type_code})")

if __name__ == "__main__":
    analyze_campaign_status()
